import { FC, useEffect, useState, useCallback } from 'react'
import { Card, Col, DatePicker, Empty, Row, Select, Spin, Statistic, Tag } from 'antd'
import { EthereumCircleColorful, BSCCircleColorful } from '@ant-design/web3-icons'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { useDashBoard } from './Hooks/useDashBoard'
import { Bar, Line, Pie } from 'react-chartjs-2'
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement, PointElement, LineElement } from 'chart.js'
import dayjs from 'dayjs'
import { DollarCircleFilled, DollarCircleOutlined, ShoppingCartOutlined, ThunderboltOutlined, UserOutlined } from '@ant-design/icons'
import { count } from 'console'
type IProps = {}
ChartJS.register(CategoryScale, LinearScale, ArcElement, BarElement, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend)

const DashBoardView: FC<IProps> = () => {
  const [selectedYear, setSelectedYear] = useState(dayjs().year())
  const [selectedMonth, setSelectedMonth] = useState<number | undefined>(undefined)

  const {
    getPackagesStats,
    packagesStats,
    getPaymentGatewayStats,
    paymentGatewayStats,
    getRevenueStats,
    revenueStats,
    getCustomerStats,
    customerStats,
    getOrderStats,
    orderStats,
    totalCustomer,
    getTotalCustomer,
    totalOrder,
    getTotalOrder,
    totalRevenue,
    getTotalRevenue,
    isLoading
  } = useDashBoard()

  const fetchStatsData = (year: number, month?: number) => {
    Promise.all([
      getPackagesStats(year, month),
      getPaymentGatewayStats(year, month),
      getRevenueStats(year, month),
      getCustomerStats(year, month),
      getOrderStats(year, month),
      getTotalCustomer(),
      getTotalOrder(),
      getTotalRevenue()
    ]).catch((err) => {
      console.log('err', err)
    })
  }

  useEffect(() => {
    console.log('isLoading', isLoading)
    fetchStatsData(selectedYear, selectedMonth)
  }, [selectedYear, selectedMonth])

  useEffect(() => {}, [packagesStats, paymentGatewayStats, revenueStats])

  return (
    <BaseView>
      <Spin spinning={isLoading}>
        <Row gutter={16} style={{ marginLeft: 20 }}>
          {/* Số lượng khách hàng */}
          <Col span={6} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card
              bordered={false}
              style={{
                width: 500,
                background: '#a3cefaff',
                boxShadow: '0 4px 16px #879bfd44',
                borderRadius: 18
              }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                <div
                  style={{
                    background: '#2563eb',
                    borderRadius: 16,
                    width: 48,
                    height: 48,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                  <UserOutlined style={{ fontSize: 28, color: '#fff' }} />
                </div>
                <div>
                  <div style={{ color: '#2563eb', fontWeight: 650, fontSize: 16, marginBottom: 6 }}>Số lượng khách hàng</div>
                  <div style={{ color: '#111827', fontWeight: 700, fontSize: 32 }}>{totalCustomer.total || 0}</div>
                </div>
              </div>
            </Card>
          </Col>
          {/* Tổng doanh thu */}
          <Col span={6} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card
              bordered={false}
              style={{
                width: 500,
                background: '#cbb4ffff',
                boxShadow: '0 4px 16px #d8b4fe44',
                borderRadius: 18
              }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                <div
                  style={{
                    background: '#a21caf',
                    borderRadius: 16,
                    width: 48,
                    height: 48,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                  <ShoppingCartOutlined style={{ fontSize: 28, color: '#fff' }} />
                </div>
                <div>
                  <div style={{ color: '#a21caf', fontWeight: 650, fontSize: 16, marginBottom: 6 }}>Số gói dịch vụ đã bán</div>
                  <div style={{ color: '#111827', fontWeight: 700, fontSize: 32 }}>{totalOrder.total || 0}</div>
                </div>
              </div>
            </Card>
          </Col>
          {/* Tổng đơn hàng */}
          <Col span={6} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card
              bordered={false}
              style={{
                width: 500,
                background: '#abf2cdff', // Xanh lá nhạt
                boxShadow: '0 4px 16px #6ee7b744',
                borderRadius: 18
              }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                <div
                  style={{
                    background: '#10b981', // Green-500
                    borderRadius: 16,
                    width: 48,
                    height: 48,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                  <DollarCircleOutlined style={{ fontSize: 28, color: '#fff' }} />
                </div>
                <div>
                  <div style={{ color: '#10b981', fontWeight: 650, fontSize: 16, marginBottom: 6 }}>Tổng doanh thu</div>
                  <div style={{ color: '#111827', fontWeight: 700, fontSize: 32 }}>${Number(totalRevenue.total || 0).toFixed(2)}</div>
                </div>
              </div>
            </Card>
          </Col>
          {/* //Phí gas */}
          <Col span={6} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card
              bordered={false}
              style={{
                width: '100%',
                background: '#faedb7ff', // Cam nhạt, năng lượng/gas
                boxShadow: '0 4px 16px #fbbf2444',
                borderRadius: 18
              }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                <div
                  style={{
                    background: '#f59e42', // Cam đậm
                    borderRadius: 16,
                    width: 48,
                    height: 48,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                  <ThunderboltOutlined style={{ fontSize: 28, color: '#fff' }} />
                </div>
                <div>
                  <div style={{ color: '#f59e42', fontWeight: 650, fontSize: 16, marginBottom: 6 }}>Tổng phí gas</div>
                  <div style={{ color: '#111827', fontWeight: 700, fontSize: 32 }}>{'Updating'}</div>
                </div>
              </div>
            </Card>
          </Col>

          {/* //BNB */}
          <Col span={12} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card
              bordered={false}
              style={{
                width: '100%',
                height: '100%',
                background: '#fffbe6', // Vàng nhạt pastel
                boxShadow: '0 4px 16px #ffe58f66',
                borderRadius: 18
              }}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between', // Đẩy hai bên
                  width: '100%',
                  gap: 16
                }}>
                {/* Bên trái: icon + estimatedGasFee */}
                <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                  <div
                    style={{
                      background: '#f3ba2f',
                      borderRadius: 16,
                      width: 48,
                      height: 48,
                      padding: 20,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                    <BSCCircleColorful style={{ fontSize: 32 }} />
                  </div>
                  <span style={{ color: '#cb9202ff', fontWeight: 700, fontSize: 16 }}>{'estimatedGasFee'}/BNB</span>
                </div>

                {/* Bên phải: link */}
                <a
                  href='https://bscscan.com/gastracker'
                  target='_blank'
                  style={{
                    color: '#111827',
                    fontWeight: 700,
                    fontSize: 16,
                    textDecoration: 'underline',
                    whiteSpace: 'nowrap'
                  }}>
                  View On BSCScan
                </a>
              </div>
            </Card>
          </Col>

          {/*ETH */}
          <Col span={12} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card
              bordered={false}
              style={{
                width: '100%',
                height: '100%',
                background: '#e5e9fd', // Xanh tím nhạt Ethereum
                boxShadow: '0 4px 16px #b3b8ee44',
                borderRadius: 18
              }}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between', // Đẩy hai bên
                  gap: 16,
                  width: '100%'
                }}>
                {/* Bên trái: icon + giá trị */}
                <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                  <div
                    style={{
                      background: '#627eea',
                      borderRadius: 16,
                      width: 48,
                      height: 48,
                      padding: 20,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                    <EthereumCircleColorful style={{ fontSize: 32 }} />
                  </div>
                  <span style={{ color: '#2646c5ff', fontWeight: 650, fontSize: 16 }}>{'estimatedGasFee'}/BNB</span>
                </div>

                {/* Bên phải: link */}
                <a
                  href='https://etherscan.io/gastracker' // Thay link động nếu cần
                  target='_blank'
                  style={{
                    color: '#111827',
                    fontWeight: 700,
                    fontSize: 16,
                    textDecoration: 'underline',
                    whiteSpace: 'nowrap'
                  }}>
                  View On Etherscan
                </a>
              </div>
            </Card>
          </Col>
        </Row>

        <Row gutter={16} style={{ marginLeft: 20 }}>
          <Col span={24} style={{ marginBottom: 20 }}>
            <b style={{ marginRight: 10 }}>Năm:</b>
            <DatePicker
              picker='year'
              defaultValue={dayjs()}
              style={{ width: 200, marginRight: 10 }}
              onChange={(value) => {
                if (!value) return
                const year = value.year()
                setSelectedYear(year)
                fetchStatsData(year, selectedMonth)
              }}
            />
            <b style={{ marginRight: 10 }}>Tháng:</b>
            <Select
              placeholder="Chọn tháng (tùy chọn)"
              style={{ width: 200 }}
              allowClear
              value={selectedMonth}
              onChange={(month) => {
                setSelectedMonth(month)
                fetchStatsData(selectedYear, month)
              }}
              options={[
                { value: 1, label: 'Tháng 1' },
                { value: 2, label: 'Tháng 2' },
                { value: 3, label: 'Tháng 3' },
                { value: 4, label: 'Tháng 4' },
                { value: 5, label: 'Tháng 5' },
                { value: 6, label: 'Tháng 6' },
                { value: 7, label: 'Tháng 7' },
                { value: 8, label: 'Tháng 8' },
                { value: 9, label: 'Tháng 9' },
                { value: 10, label: 'Tháng 10' },
                { value: 11, label: 'Tháng 11' },
                { value: 12, label: 'Tháng 12' }
              ]}
            />
          </Col>

          {/* Thống kê số lượng khách hàng */}
          <Col span={12} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card style={{ width: '100%', height: 600 }} title='Thống kê số lượng khách hàng'>
              <Row gutter={16} style={{ height: '500px', display: 'flex', justifyContent: 'center' }}>
                <Col span={24}>
                  {customerStats.datasets?.length > 0 ? (
                    <Line
                      data={{
                        labels: customerStats.labels,
                        datasets: [
                          ...customerStats.datasets,
                        ]
                      }}
                      options={{
                        plugins: {
                          legend: {
                            display: false,
                            labels: {
                              font: {
                                size: 16,
                                weight: 'bold'
                              }
                            }
                          }
                        },
                        scales: {
                          x: {
                            ticks: {
                              font: {
                                size: 14
                              }
                            },
                            title: {
                              display: true,
                              text: 'Tháng',
                              font: {
                                size: 16
                              }
                            }
                          },
                          y: {
                            ticks: {
                              stepSize: 1,

                              font: {
                                size: 15
                              }
                            },
                            title: {
                              display: true,
                              text: 'Số lượng khách hàng',
                              font: {
                                size: 16
                              }
                            }
                          }
                        }
                      }}
                    />
                  ) : (
                    <Empty description='No Data' />
                  )}
                </Col>
              </Row>
            </Card>
          </Col>

          {/* Thống kê số lượng đơn hàng */}
          <Col span={12} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card style={{ width: '100%', height: 600 }} title='Thống kê số lượng đơn hàng'>
              <Row gutter={16} style={{ height: '500px', display: 'flex', justifyContent: 'center' }}>
                <Col span={24}>
                  {orderStats?.datasets?.[0]?.data?.find((item: any) => item > 0) ? (
                    <Line
                      data={{
                        labels: orderStats.labels,
                        datasets: [
                          {
                            ...orderStats.datasets[0],
                            backgroundColor: '#e2d27aff', // xanh ngọc nhạt fill
                            borderColor: '#e2d27aff', // xanh ngọc
                            pointBackgroundColor: '#bd8d1cff',
                            pointBorderColor: '#fff',
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            tension: 0.4
                          }
                        ]
                      }}
                      options={{
                        plugins: {
                          legend: {
                            display: false,
                            labels: {
                              font: {
                                size: 16,
                                weight: 'bold'
                              }
                            }
                          },
                          tooltip: {
                            callbacks: {
                              label: function (context) {
                                // Format số VNĐ
                                const value = context.raw
                                return value.toLocaleString() + ' USD'
                              }
                            }
                          }
                        },
                        scales: {
                          x: {
                            title: {
                              display: true,
                              text: 'Tháng',
                              font: { size: 16 }
                            },
                            ticks: {
                              font: { size: 14 }
                            }
                          },
                          y: {
                            title: {
                              display: true,
                              text: 'Đơn hàng',
                              font: { size: 16 }
                            },
                            ticks: {
                              stepSize: 1,

                              font: { size: 14 }
                            }
                          }
                        }
                      }}
                    />
                  ) : (
                    <Empty description='No Data' />
                  )}
                </Col>
              </Row>
            </Card>
          </Col>
          {/* Thống kê gói dịch vụ */}
          <Col span={24} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card style={{ width: '100%', height: 600 }} title='Thống kê gói dịch vụ'>
              {packagesStats?.pieChart?.datasets?.length > 0 ? (
                <Row gutter={16}>
                  <Col span={12} style={{ height: '350px', display: 'flex', justifyContent: 'center' }}>
                    {packagesStats?.pieChart?.datasets?.length > 0 && (
                      <Pie
                        data={packagesStats?.pieChart}
                        options={{
                          plugins: {
                            legend: {
                              display: true,
                              labels: {
                                padding: 15,
                                font: {
                                  size: 16,
                                  weight: 'bold'
                                }
                              }
                            }
                          }
                        }}
                      />
                    )}
                  </Col>

                  {/* Thống kê số lượng gói dịch vụ theo tháng */}
                  <Col span={12}>
                    {packagesStats?.barChart?.datasets?.length > 0 && (
                      <Bar
                        data={{
                          ...packagesStats.barChart,
                          datasets: packagesStats.barChart.datasets.map((ds) => ({
                            ...ds,
                            borderRadius: 8 // bo góc bar nhìn đẹp hơn (tùy thích)
                          }))
                        }}
                        options={{
                          plugins: {
                            legend: {
                              display: true,
                              labels: {
                                padding: 15,
                                font: {
                                  size: 16,
                                  weight: 'bold'
                                }
                              }
                            }
                          }
                        }}
                      />
                    )}
                  </Col>
                </Row>
              ) : (
                <Empty description='No Data' />
              )}
            </Card>
          </Col>

          {/* Thống kê số lượng giao dịch (Off-Chain) */}
          <Col span={12} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card style={{ width: '100%', height: 600 }} title='Thống kê số lượng giao dịch (Off-Chain)'>
              <Row gutter={16} style={{ height: '500px', display: 'flex', justifyContent: 'center' }}>
                <Col span={24}>
                  {paymentGatewayStats.datasets?.length > 0 ? (
                    <Line
                      data={{
                        labels: paymentGatewayStats.labels,
                        datasets: [
                          {
                            ...paymentGatewayStats.datasets[0],
                            backgroundColor: '#8e77eaff', // nhạt để fill
                            borderColor: '#8e77eaff', // màu viền line chính
                            pointBackgroundColor: '#8e77eaff',
                            pointHoverRadius: 5,
                            pointRadius: 4,
                            tension: 0.4 // đường cong mượt
                          }
                        ]
                      }}
                      options={{
                        plugins: {
                          legend: {
                            display: true,
                            labels: {
                              font: {
                                size: 16,
                                weight: 'bold'
                              }
                            }
                          }
                        },
                        scales: {
                          x: {
                            ticks: {
                              font: {
                                size: 14
                              }
                            },
                            title: {
                              display: true,
                              text: 'Tháng',
                              font: {
                                size: 16
                              }
                            }
                          },
                          y: {
                            ticks: {
                              stepSize: 1,
                              callback: (value: any) => `${value} Giao dịch`,
                              font: {
                                size: 15
                              }
                            },
                            title: {
                              display: true,
                              text: 'Số lượng giao dịch',
                              font: {
                                size: 16
                              }
                            }
                          }
                        }
                      }}
                    />
                  ) : (
                    <Empty description='No Data' />
                  )}
                </Col>
              </Row>
            </Card>
          </Col>
          {/* Thống kê doanh thu */}
          <Col span={12} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card style={{ width: '100%', height: 600 }} title='Thống kê doanh thu'>
              <Row gutter={16} style={{ height: '500px', display: 'flex', justifyContent: 'center' }}>
                <Col span={24}>
                  {revenueStats?.datasets?.[0]?.data?.find((item: any) => item > 0) ? (
                    <Line
                      data={{
                        labels: revenueStats.labels,
                        datasets: [
                          {
                            ...revenueStats.datasets[0],
                            backgroundColor: 'rgba(16, 185, 129, 0.1)', // xanh ngọc nhạt fill
                            borderColor: '#10B981', // xanh ngọc
                            pointBackgroundColor: '#10B981',
                            pointBorderColor: '#fff',
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            tension: 0.4
                          }
                        ]
                      }}
                      options={{
                        plugins: {
                          legend: {
                            display: false,
                            labels: {
                              font: {
                                size: 16,
                                weight: 'bold'
                              }
                            }
                          },
                          tooltip: {
                            callbacks: {
                              label: function (context) {
                                // Format số VNĐ
                                const value = context.raw
                                return value.toLocaleString() + ' USD'
                              }
                            }
                          }
                        },
                        scales: {
                          x: {
                            title: {
                              display: true,
                              text: 'Tháng',
                              font: { size: 16 }
                            },
                            ticks: {
                              font: { size: 14 }
                            }
                          },
                          y: {
                            title: {
                              display: true,
                              text: 'Doanh thu (USD)',
                              font: { size: 16 }
                            },
                            ticks: {
                              callback: function (value) {
                                return value.toLocaleString() + ' USD'
                              },
                              font: { size: 14 }
                            }
                          }
                        }
                      }}
                    />
                  ) : (
                    <Empty description='No Data' />
                  )}
                </Col>
              </Row>
            </Card>
          </Col>
          {/* Thống kê số lượng giao dịch (On-Chain) */}
          <Col span={12} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card style={{ width: '100%', height: 600 }} title='Thống kê số lượng giao dịch (On-Chain)'>
              <Row gutter={16} style={{ height: '500px', display: 'flex', justifyContent: 'center' }}>
                {/* <Col span={24} >
              {paymentGatewayStats.datasets?.length > 0 ? (
                <Line
                  data={{
                    labels: paymentGatewayStats.labels,
                    datasets: [
                      {
                        ...paymentGatewayStats.datasets[0],
                        backgroundColor: 'rgba(59, 130, 246, 0.1)', // nhạt để fill
                        borderColor: '#3B82F6', // màu viền line chính
                        pointBackgroundColor: '#3B82F6',
                        pointBorderColor: '#ffffff',
                        pointHoverRadius: 6,
                        pointRadius: 4,
                        tension: 0.4 // đường cong mượt
                      }
                    ]
                  }}
                  options={{
                    plugins: {
                      legend: {
                        display: true,
                        labels: {
                          font: {
                            size: 16,
                            weight: 'bold'
                          }
                        }
                      }
                    },
                    scales: {
                      x: {
                        ticks: {
                          font: {
                            size: 14
                          }
                        },
                        title: {
                          display: true,
                          text: 'Tháng',
                          font: {
                            size: 16
                          }
                        }
                      },
                      y: {
                        ticks: {
                          stepSize: 1,
                          callback: (value: any) => `${value} Giao dịch`,
                          font: {
                            size: 15
                          }
                        },
                        title: {
                          display: true,
                          text: 'Số lượng giao dịch',
                          font: {
                            size: 16
                          }
                        }
                      }
                    }
                  }}
                />
              ) : (
                <Empty description='No Data' />
              )}
            </Col> */}
                <Empty description='No Data' />
              </Row>
            </Card>
          </Col>
          <Col span={12} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card style={{ width: '100%', height: 600 }} title='Thống kê gas hằng tháng'>
              <Row gutter={16} style={{ height: '500px', display: 'flex', justifyContent: 'center' }}>
                {/* <Col span={24} >
              {revenueStats?.datasets?.[0]?.data?.find((item: any) => item > 0) ? (
                <Line
                  data={{
                    labels: revenueStats.labels,
                    datasets: [
                      {
                        ...revenueStats.datasets[0],
                        backgroundColor: 'rgba(16, 185, 129, 0.1)', // xanh ngọc nhạt fill
                        borderColor: '#10B981', // xanh ngọc
                        pointBackgroundColor: '#10B981',
                        pointBorderColor: '#fff',
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        tension: 0.4
                      }
                    ]
                  }}
                  options={{
                    plugins: {
                      legend: {
                        display: true,
                        labels: {
                          font: {
                            size: 16,
                            weight: 'bold'
                          }
                        }
                      },
                      tooltip: {
                        callbacks: {
                          label: function (context) {
                            // Format số VNĐ
                            const value = context.raw
                            return value.toLocaleString() + ' USD'
                          }
                        }
                      }
                    },
                    scales: {
                      x: {
                        title: {
                          display: true,
                          text: 'Tháng',
                          font: { size: 16 }
                        },
                        ticks: {
                          font: { size: 14 }
                        }
                      },
                      y: {
                        title: {
                          display: true,
                          text: 'Doanh thu (USD)',
                          font: { size: 16 }
                        },
                        ticks: {
                          callback: function (value) {
                            return value.toLocaleString() + ' USD'
                          },
                          font: { size: 14 }
                        }
                      }
                    }
                  }}
                />
              ) : (
                <Empty description='No Data' />
              )}
            </Col> */}
                <Empty description='No Data' />
              </Row>
            </Card>
          </Col>
        </Row>
      </Spin>
    </BaseView>
  )
}

export default DashBoardView
