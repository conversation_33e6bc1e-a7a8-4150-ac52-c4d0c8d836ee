import { environment } from '~/environments/environment'
import { rootApiService } from '../@common'
import { IMember, IMemberFilter, IMemberList } from '~/dto/member.dto'
import { IMemberConfigPackageResponse, IMemberSubscriptionPlan, IMemberSubscriptionPlanResponse, ISubscriptionPlan } from '~/dto/subscriptionPlan.dto'
import { IOrder, IOrderRespone } from '~/dto/order.dto'

class DashBoardService {
  constructor() {}
  backEnd = environment.backEnd
  APIs = {
    //total customer
    TOTAL_CUSTOMER: `${this.backEnd}/api/admin/dashboard/total-customer`,
    TOTAL_ORDER: `${this.backEnd}/api/admin/dashboard/total-order`,
    TOTAL_REVENUE: `${this.backEnd}/api/admin/dashboard/total-revenue`,
    TOTAL_GAS: `${this.backEnd}/api/admin/dashboard/total-gas`,
    //customer
    CUSTOMER_STATS: `${this.backEnd}/api/admin/dashboard/customer-stats`,
    //order-stats
    ORDER_STATS: `${this.backEnd}/api/admin/dashboard/order-stats`,
    //packages-stats
    PACKAGES_STATS: `${this.backEnd}/api/admin/dashboard/package-statistics`,
    //payment-gateway-stats
    PAYMENT_GATEWAY_STATS: `${this.backEnd}/api/admin/dashboard/payment-gateway-stats`,
    //revenue-stats
    REVENUE_STATS: `${this.backEnd}/api/admin/dashboard/revenue-stats`
  }

  async getTotalCustomer(): Promise<any> {
    return await rootApiService.get(this.APIs.TOTAL_CUSTOMER)
  }

  async getTotalOrder(): Promise<any> {
    return await rootApiService.get(this.APIs.TOTAL_ORDER)
  }

  async getTotalRevenue(): Promise<any> {
    return await rootApiService.get(this.APIs.TOTAL_REVENUE)
  }

  async getCustomerStats(year: any, month?: any): Promise<any> {
    const params = month ? { year, month } : { year }
    return await rootApiService.get(this.APIs.CUSTOMER_STATS, params)
  }

  async getOrderStats(year: any, month?: any): Promise<any> {
    const params = month ? { year, month } : { year }
    return await rootApiService.get(this.APIs.ORDER_STATS, params)
  }

  async getPackagesStats(year: any, month?: any): Promise<any> {
    const params = month ? { year, month } : { year }
    return await rootApiService.get(this.APIs.PACKAGES_STATS, params)
  }

  async getPaymentGatewayStats(year: any, month?: any): Promise<any> {
    const params = month ? { year, month } : { year }
    return await rootApiService.get(this.APIs.PAYMENT_GATEWAY_STATS, params)
  }

  async getRevenueStats(year: any, month?: any): Promise<any> {
    const params = month ? { year, month } : { year }
    return await rootApiService.get(this.APIs.REVENUE_STATS, params)
  }
}

export const dashBoardService = new DashBoardService()
